#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单拆分处理脚本
功能：当U列（队友）存在内容时，将订单拆分为两个订单，并将总金额平分
"""

import pandas as pd
import re
import sys
from pathlib import Path

def extract_amount(amount_str):
    """从金额字符串中提取数值"""
    if pd.isna(amount_str) or amount_str == '':
        return 0.0
    # 使用正则表达式提取数字部分
    match = re.search(r'(\d+\.?\d*)', str(amount_str))
    if match:
        return float(match.group(1))
    return 0.0

def format_amount(amount):
    """将数值格式化为金额字符串，保持精确的小数位数，不进行四舍五入"""
    if amount == 0:
        return ""
    # 使用字符串格式化避免浮点数精度问题，保留足够的小数位
    formatted = f"{amount:.10f}".rstrip('0').rstrip('.')
    return f"{formatted}元"

def is_exclusive_player(player_name, exclusive_list):
    """
    检查陪玩是否在独家名单中

    Args:
        player_name: 陪玩名称
        exclusive_list: 独家陪玩名单（可能包含多个名字，用逗号或其他分隔符分隔）

    Returns:
        bool: 如果在独家名单中返回True，否则返回False
    """
    if pd.isna(player_name) or pd.isna(exclusive_list):
        return False

    player_name = str(player_name).strip()
    exclusive_list = str(exclusive_list).strip()

    if not player_name or not exclusive_list:
        return False

    # 检查陪玩名称是否在独家名单中
    # 支持多种分隔符：逗号、顿号、空格、换行等
    import re
    exclusive_names = re.split(r'[,，、\s\n\r]+', exclusive_list)
    exclusive_names = [name.strip() for name in exclusive_names if name.strip()]

    # 直接匹配或者检查是否包含
    if player_name in exclusive_names:
        return True

    # 检查是否有任何独家名单中的名字包含当前陪玩名称
    for exclusive_name in exclusive_names:
        if player_name in exclusive_name or exclusive_name in player_name:
            return True

    return False

def calculate_player_income(total_amount, player_name, exclusive_list):
    """
    根据陪玩是否在独家名单中计算收入

    Args:
        total_amount: 折后总计金额
        player_name: 陪玩名称
        exclusive_list: 独家陪玩名单

    Returns:
        float: 计算后的陪玩收入
    """
    if is_exclusive_player(player_name, exclusive_list):
        return total_amount * 0.85  # 独家陪玩85%
    else:
        return total_amount * 0.8   # 普通陪玩80%

def generate_player_income_summary(processed_df, output_file):
    """
    生成每个陪玩的收入汇总表

    Args:
        processed_df: 处理后的DataFrame
        output_file: 输出CSV文件路径
    """
    player_info_col = processed_df.columns[16]  # Q列：陪玩信息
    player_income_col = processed_df.columns[6]  # G列：陪玩收入
    exclusive_list_col = processed_df.columns[46] # AU列：独家陪玩名单

    # 收集所有独家陪玩名单
    all_exclusive_players = set()
    for index, row in processed_df.iterrows():
        exclusive_list = row[exclusive_list_col]
        if pd.notna(exclusive_list) and str(exclusive_list).strip():
            exclusive_list_str = str(exclusive_list).strip()
            # 将独家名单按常见分隔符分割
            import re
            exclusive_names = re.split(r'[,，、\s\n\r]+', exclusive_list_str)
            exclusive_names = [name.strip() for name in exclusive_names if name.strip()]
            all_exclusive_players.update(exclusive_names)

    print(f"发现的所有独家陪玩: {sorted(all_exclusive_players)}")

    # 存储每个陪玩的收入数据
    player_summary = {}

    for index, row in processed_df.iterrows():
        player_name = row[player_info_col]
        income_str = row[player_income_col]
        exclusive_list = row[exclusive_list_col]

        # 跳过空的陪玩名称
        if pd.isna(player_name) or str(player_name).strip() == '':
            continue

        player_name = str(player_name).strip()

        # 提取收入金额
        income_amount = extract_amount(income_str)

        # 判断是否为独家陪玩（检查该陪玩是否在该行的AU列中）
        is_exclusive = is_exclusive_player(player_name, exclusive_list)

        # 初始化陪玩数据
        if player_name not in player_summary:
            # 检查该陪玩是否在任何一行中被标记为独家
            is_player_exclusive = player_name in all_exclusive_players
            player_summary[player_name] = {
                'player_name': player_name,
                'total_income': 0.0,
                'order_count': 0,
                'is_exclusive': is_player_exclusive,
                'commission_rate': '85%' if is_player_exclusive else '80%'
            }

        # 累加收入和订单数
        player_summary[player_name]['total_income'] += income_amount
        player_summary[player_name]['order_count'] += 1

    # 转换为DataFrame
    summary_data = []
    for player_data in player_summary.values():
        summary_data.append({
            '陪玩姓名': player_data['player_name'],
            '总收入': f"{player_data['total_income']:.2f}元",
            '订单数量': player_data['order_count'],
            '是否独家': '是' if player_data['is_exclusive'] else '否',
            '提成比例': player_data['commission_rate'],
            '平均每单收入': f"{player_data['total_income'] / player_data['order_count']:.2f}元" if player_data['order_count'] > 0 else "0.00元"
        })

    # 按总收入降序排列
    summary_df = pd.DataFrame(summary_data)
    summary_df['总收入_数值'] = summary_df['总收入'].apply(extract_amount)
    summary_df = summary_df.sort_values('总收入_数值', ascending=False)
    summary_df = summary_df.drop('总收入_数值', axis=1)

    # 保存汇总表
    try:
        summary_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"陪玩收入汇总表已保存到: {output_file}")
        print(f"  统计陪玩数量: {len(summary_df)}")
        print(f"  独家陪玩数量: {len(summary_df[summary_df['是否独家'] == '是'])}")
        print(f"  普通陪玩数量: {len(summary_df[summary_df['是否独家'] == '否'])}")
        return True
    except Exception as e:
        print(f"保存陪玩收入汇总表时出错: {e}")
        return False

def process_csv_file(input_file, output_file=None, generate_summary=True):
    """
    处理CSV文件，拆分包含队友的订单，重新计算陪玩收入，并生成收入汇总表

    Args:
        input_file: 输入CSV文件路径
        output_file: 输出CSV文件路径，如果为None则覆盖原文件
        generate_summary: 是否生成陪玩收入汇总表
    """

    # 读取CSV文件
    try:
        df = pd.read_csv(input_file, encoding='utf-8')
    except UnicodeDecodeError:
        try:
            df = pd.read_csv(input_file, encoding='gbk')
        except UnicodeDecodeError:
            df = pd.read_csv(input_file, encoding='utf-8-sig')

    print(f"原始数据行数: {len(df)}")

    # 获取列名
    columns = df.columns.tolist()
    print(f"列数: {len(columns)}")

    # 确认关键列的索引
    # F列：折后总计 (索引5)
    # G列：陪玩收入 (索引6)
    # H列：单个陪玩收入 (索引7)
    # Q列：陪玩信息 (索引16)
    # U列：队友 (索引20)
    # AU列：独家陪玩名单 (索引46)

    if len(columns) < 47:
        print("错误：CSV文件列数不足，请检查文件格式")
        return

    total_amount_col = columns[5]   # F列：折后总计
    player_income_col = columns[6]  # G列：陪玩收入
    single_player_income_col = columns[7]  # H列：单个陪玩收入
    player_info_col = columns[16]   # Q列：陪玩信息
    teammate_col = columns[20]      # U列：队友
    exclusive_list_col = columns[46] # AU列：独家陪玩名单

    print(f"处理的关键列:")
    print(f"  F列 ({total_amount_col}): 折后总计")
    print(f"  G列 ({player_income_col}): 陪玩收入")
    print(f"  H列 ({single_player_income_col}): 单个陪玩收入")
    print(f"  Q列 ({player_info_col}): 陪玩信息")
    print(f"  U列 ({teammate_col}): 队友")
    print(f"  AU列 ({exclusive_list_col}): 独家陪玩名单")

    # 收集所有独家陪玩名单
    all_exclusive_players = set()
    for index, row in df.iterrows():
        exclusive_list = row[exclusive_list_col]
        if pd.notna(exclusive_list) and str(exclusive_list).strip():
            exclusive_list_str = str(exclusive_list).strip()
            # 将独家名单按常见分隔符分割
            import re
            exclusive_names = re.split(r'[,，、\s\n\r]+', exclusive_list_str)
            exclusive_names = [name.strip() for name in exclusive_names if name.strip()]
            all_exclusive_players.update(exclusive_names)

    print(f"处理时发现的所有独家陪玩: {sorted(all_exclusive_players)}")

    # 存储处理后的数据
    processed_rows = []
    split_count = 0
    recalculated_count = 0

    for index, row in df.iterrows():
        teammate = row[teammate_col]
        exclusive_list = row[exclusive_list_col]

        # 检查U列是否有内容（队友信息）
        if pd.notna(teammate) and str(teammate).strip() != '':
            split_count += 1
            print(f"处理第 {index+2} 行，拆分订单，队友: {teammate}")

            # 获取原始金额
            original_amount_str = row[total_amount_col]
            original_amount = extract_amount(original_amount_str)
            half_amount = original_amount / 2

            # 创建第一行：原内容基本不变，但清空U列队友
            row1 = row.copy()
            row1[teammate_col] = ''  # 清空队友列
            row1[total_amount_col] = format_amount(half_amount)  # 金额减半

            # 重新计算第一行的陪玩收入（检查是否在所有独家陪玩中）
            player1_name = row1[player_info_col]
            if str(player1_name).strip() in all_exclusive_players:
                player1_income = half_amount * 0.85  # 独家陪玩85%
            else:
                player1_income = half_amount * 0.8   # 普通陪玩80%
            row1[player_income_col] = format_amount(player1_income)
            row1[single_player_income_col] = format_amount(player1_income)

            # 创建第二行：Q列陪玩信息改为原来的U列队友名称，其余和第一行一样
            row2 = row1.copy()
            row2[player_info_col] = teammate  # Q列改为原队友名称
            row2[total_amount_col] = format_amount(half_amount)  # 金额减半

            # 重新计算第二行的陪玩收入（检查是否在所有独家陪玩中）
            if str(teammate).strip() in all_exclusive_players:
                player2_income = half_amount * 0.85  # 独家陪玩85%
            else:
                player2_income = half_amount * 0.8   # 普通陪玩80%
            row2[player_income_col] = format_amount(player2_income)
            row2[single_player_income_col] = format_amount(player2_income)

            # 添加两行到结果中
            processed_rows.append(row1)
            processed_rows.append(row2)
            recalculated_count += 2

        else:
            # U列没有内容，重新计算陪玩收入
            row_copy = row.copy()
            original_amount_str = row_copy[total_amount_col]
            original_amount = extract_amount(original_amount_str)
            player_name = row_copy[player_info_col]

            # 重新计算陪玩收入（检查是否在所有独家陪玩中）
            if str(player_name).strip() in all_exclusive_players:
                player_income = original_amount * 0.85  # 独家陪玩85%
            else:
                player_income = original_amount * 0.8   # 普通陪玩80%
            row_copy[player_income_col] = format_amount(player_income)
            row_copy[single_player_income_col] = format_amount(player_income)

            processed_rows.append(row_copy)
            recalculated_count += 1
    
    # 创建新的DataFrame
    result_df = pd.DataFrame(processed_rows, columns=columns)
    
    print(f"处理完成:")
    print(f"  拆分的订单数: {split_count}")
    print(f"  重新计算收入的行数: {recalculated_count}")
    print(f"  处理后数据行数: {len(result_df)}")
    print(f"  新增行数: {len(result_df) - len(df)}")
    
    # 保存结果
    if output_file is None:
        output_file = input_file

    try:
        result_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"结果已保存到: {output_file}")
    except Exception as e:
        print(f"保存文件时出错: {e}")
        return False

    # 生成陪玩收入汇总表
    if generate_summary:
        # 生成汇总表文件名
        base_name = output_file.rsplit('.', 1)[0] if '.' in output_file else output_file
        summary_file = f"{base_name}_陪玩收入汇总.csv"

        print(f"\n开始生成陪玩收入汇总表...")
        generate_player_income_summary(result_df, summary_file)

    return True

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python process_orders.py <输入文件> [输出文件] [--no-summary]")
        print("示例: python process_orders.py 财务表_20250721123952.csv")
        print("示例: python process_orders.py 财务表_20250721123952.csv 处理后的财务表.csv")
        print("示例: python process_orders.py 财务表_20250721123952.csv 处理后的财务表.csv --no-summary")
        print("参数说明:")
        print("  --no-summary: 不生成陪玩收入汇总表")
        return

    input_file = sys.argv[1]
    output_file = None
    generate_summary = True

    # 解析命令行参数
    for i in range(2, len(sys.argv)):
        arg = sys.argv[i]
        if arg == '--no-summary':
            generate_summary = False
        elif not output_file and not arg.startswith('--'):
            output_file = arg

    # 检查输入文件是否存在
    if not Path(input_file).exists():
        print(f"错误：输入文件 '{input_file}' 不存在")
        return

    print(f"开始处理文件: {input_file}")
    if output_file:
        print(f"输出文件: {output_file}")
    else:
        print("将覆盖原文件")

    if generate_summary:
        print("将生成陪玩收入汇总表")
    else:
        print("跳过生成陪玩收入汇总表")

    # 处理文件
    success = process_csv_file(input_file, output_file, generate_summary)

    if success:
        print("\n🎉 处理完成！")
    else:
        print("\n❌ 处理失败！")

if __name__ == "__main__":
    main()
